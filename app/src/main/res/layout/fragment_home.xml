<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".ui.home.HomeFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="0dp">

        <!-- Hero Section -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="400dp"
            android:background="@color/hero_background"
            android:padding="24dp">

            <!-- Dancer Illustration -->
            <ImageView
                android:id="@+id/dancer_illustration"
                android:layout_width="200dp"
                android:layout_height="250dp"
                android:layout_marginTop="32dp"
                android:contentDescription="@string/dancer_illustration_desc"
                android:src="@drawable/dancer_illustration"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Title -->
            <TextView
                android:id="@+id/title_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:fontFamily="serif"
                android:text="@string/app_title"
                android:textColor="@color/text_primary"
                android:textSize="32sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/dancer_illustration" />

            <!-- Subtitle -->
            <TextView
                android:id="@+id/subtitle_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center"
                android:text="@string/app_subtitle"
                android:textColor="@color/text_secondary"
                android:textSize="16sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title_text" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- What We Offer Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:text="@string/what_we_offer"
                android:textColor="@color/text_primary"
                android:textSize="24sp"
                android:textStyle="bold" />

            <!-- Features Grid -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="2">

                <!-- Feature 1: Variety of Dance Styles -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:layout_weight="1"
                    android:background="@drawable/feature_card_background"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_marginBottom="8dp"
                        android:contentDescription="@string/music_icon_desc"
                        android:src="@drawable/ic_music_note"
                        android:tint="@color/accent_color" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="@string/variety_dance_styles"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                </LinearLayout>

                <!-- Feature 2: All Skill Levels Welcome -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_weight="1"
                    android:background="@drawable/feature_card_background"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_marginBottom="8dp"
                        android:contentDescription="@string/people_icon_desc"
                        android:src="@drawable/ic_people"
                        android:tint="@color/accent_color" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="@string/all_skill_levels"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

            <!-- Feature 3: Flexible Schedules -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/feature_card_background"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="16dp">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginEnd="12dp"
                    android:contentDescription="@string/schedule_icon_desc"
                    android:src="@drawable/ic_schedule"
                    android:tint="@color/accent_color" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/flexible_schedules"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

        <!-- Testimonials Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/testimonial_background"
            android:orientation="vertical"
            android:padding="24dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:text="@string/hear_from_dancers"
                android:textColor="@color/text_primary"
                android:textSize="24sp"
                android:textStyle="bold" />

            <!-- Testimonial 1 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:background="@drawable/testimonial_card_background"
                android:orientation="horizontal"
                android:padding="16dp">

                <ImageView
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/avatar_background"
                    android:contentDescription="@string/sarah_avatar_desc"
                    android:src="@drawable/avatar_sarah" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="4dp"
                        android:text="@string/testimonial_sarah"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/sarah_name"
                        android:textColor="@color/accent_color"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

            <!-- Testimonial 2 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/testimonial_card_background"
                android:orientation="horizontal"
                android:padding="16dp">

                <ImageView
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/avatar_background"
                    android:contentDescription="@string/emily_avatar_desc"
                    android:src="@drawable/avatar_emily" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="4dp"
                        android:text="@string/testimonial_emily"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/emily_name"
                        android:textColor="@color/accent_color"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!-- Register Button Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="32dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/register_button"
                android:layout_width="200dp"
                android:layout_height="56dp"
                android:backgroundTint="@color/accent_color"
                android:text="@string/register_here"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                app:cornerRadius="28dp" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
