<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/image_view_item_transform"
        android:layout_width="@dimen/item_transform_image_length"
        android:layout_height="@dimen/item_transform_image_length"
        android:layout_margin="8dp"
        android:contentDescription="@string/image_view_item_transform_content_description"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@tools:sample/avatars" />

    <TextView
        android:id="@+id/text_view_item_transform"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/image_view_item_transform"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="This is item # xx" />
</androidx.constraintlayout.widget.ConstraintLayout>