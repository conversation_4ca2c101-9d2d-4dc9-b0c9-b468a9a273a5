<?xml version="1.0" encoding="utf-8"?><!--
Wrap the DrawerLayout with FrameLayout to use the same View type for the same view ID
across the layout configurations
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/activity_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.drawerlayout.widget.DrawerLayout
        android:id="@+id/drawer_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true">

        <include
            android:id="@+id/app_bar_main"
            layout="@layout/app_bar_main"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </androidx.drawerlayout.widget.DrawerLayout>
</FrameLayout>