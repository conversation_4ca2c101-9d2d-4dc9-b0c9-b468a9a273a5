<!--
  ~ Copyright 2015 Google Inc. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="128dp"
    android:height="128dp"
    android:viewportWidth="128.0"
    android:viewportHeight="128.0">
    <path
        android:fillColor="#FFCC80"
        android:pathData="M41.6 123.8s0 .1,-.1.1l.3,-.4c-.1.2,-.1.2,-.2.3z" />
    <path
        android:fillColor="#FFFF8D"
        android:pathData="M0 0h128v128h-128z" />
    <path
        android:fillColor="#F4B400"
        android:pathData="M110.3 91.4l-.5,-.5c.1.2.3.4.5.5zM105.9 86.9l.4.5c-.1,-.2,-.3,-.4,-.4,-.5zM115.7 96.4c.2.2.4.4.7.6,-.3,-.2,-.5,-.4,-.7,-.6zM104.3 85c.2.2.3.4.5.5,-.2,-.1,-.4,-.3,-.5,-.5zM108 89.1zM124.9 103.1l.9.6c-.3,-.3,-.6,-.4,-.9,-.6zM96.4 74.1l.1.2c-.1 0,-.1,-.1,-.1,-.2zM111.6 92.7c.2.2.4.4.7.6l-.7,-.6zM121.6 100.9l.9.6,-.9,-.6zM102.8 83.2l.4.5c-.1,-.1,-.3,-.3,-.4,-.5zM101.4 81.5c.1.1.1.2.2.2,-.1 0,-.1,-.1,-.2,-.2zM113.6 94.5l.7.6c-.2,-.1,-.5,-.4,-.7,-.6zM98.6 77.7c.1.1.1.2.2.2l-.2,-.2zM97.4 75.9l.2.3c0,-.1,-.1,-.2,-.2,-.3zM125.8 103.6l-.9,-.6,-2.4,-1.5,-.9,-.6c-1,-.7,-2.1,-1.4,-3.1,-2.2l-2.2,-1.8c-.2,-.2,-.4,-.4,-.7,-.6,-.5,-.4,-1,-.8,-1.4,-1.2l-.7,-.6,-1.3,-1.2,-.7,-.6c-.5,-.4,-.9,-.9,-1.3,-1.3l-.5,-.5,-1.8,-1.8c-.6,-.6,-1.1,-1.2,-1.6,-1.8l-.4,-.5,-1.2,-1.3c-.2,-.2,-.3,-.4,-.5,-.5l-1.1,-1.3,-.4,-.5,-1.2,-1.5c-.1,-.1,-.1,-.2,-.2,-.2,-.9,-1.2,-1.8,-2.4,-2.6,-3.6,-.1,-.1,-.1,-.2,-.2,-.2l-1,-1.5,-.2,-.3c-.3,-.5,-.6,-1,-1,-1.5l-.1,-.2c-1.1,-1.8,-2,-3.5,-2.9,-5.2,-3.1,-6,-4.8,-11.4,-5.7,-15.9,-.2,-.9,-.3,-1.7,-.4,-2.6l-2.2,-37.5,-8 10.2c-3.7,-2.6,-8.2,-4.2,-13.1,-4.2s-9.4 1.5,-13.1 4.1l-8.1,-10.1,-1.8 29,-7.7 71.8h.2c-.1 1,-.2 2,-.2 3 0 4 .8 7.7 2.1 11.2h92.7v-23.1c-.7,-.4,-1.5,-.8,-2.2,-1.3zm-70.8,-65.2c-.9 0,-1.6,-.7,-1.6,-1.6 0,-.9.7,-1.6 1.6,-1.6.9 0 1.6.7 1.6 1.6 0 .8,-.7 1.6,-1.6 1.6zm17.9 0c-.9 0,-1.6,-.7,-1.6,-1.6 0,-.9.7,-1.6 1.6,-1.6.9 0 1.6.7 1.6 1.6 0 .8,-.7 1.6,-1.6 1.6z" />
    <path
        android:fillColor="#444"
        android:pathData="M72.9,36.7 m-2.0, 0 a 2.0,2.0 0 1,1 4.0,0 a2.0,2.0 0 1,1 -4.0,0" />
    <path
        android:fillColor="#444"
        android:pathData="M55.0,36.7 m-2.0, 0 a 2.0,2.0 0 1,1 4.0,0 a2.0,2.0 0 1,1 -4.0,0" />
    <path
        android:fillColor="#444"
        android:pathData="M61.6 39.5c-.5 1,-.1 1.7 1 1.7h4.6c1.1 0 1.6,-.8 1,-1.7l-2.3,-4c-.5,-1,-1.4,-1,-2 0l-2.3 4z" />
    <path
        android:fillColor="#FF5722"
        android:pathData="M92.5 102.7c8.3 11.3 23.6 14.4 35.5 7.8v-5.6l-2.2,-1.3,-.9,-.6,-2.4,-1.5,-.9,-.6c-1,-.7,-2.1,-1.4,-3.1,-2.2l-2.2,-1.8c-.2,-.2,-.4,-.4,-.7,-.6,-.5,-.4,-1,-.8,-1.4,-1.2l-.7,-.6,-1.3,-1.2,-.7,-.6c-.5,-.4,-.9,-.9,-1.3,-1.3l-.5,-.5,-1.8,-1.8c-.6,-.6,-1.1,-1.2,-1.6,-1.8l-.4,-.5,-1.2,-1.3c-.2,-.2,-.3,-.4,-.5,-.5l-1.1,-1.3,-.4,-.5,-1.2,-1.5c-.1,-.1,-.1,-.2,-.2,-.2,-.9,-1.2,-1.8,-2.4,-2.6,-3.6,-.1,-.1,-.1,-.2,-.2,-.2l-1,-1.5,-.2,-.3c-.3,-.5,-.6,-1,-1,-1.5l-.1,-.2c-1.1,-1.8,-2,-3.5,-2.9,-5.2,-7.7 9.4,-8.4 23.4,-.8 33.7z" />
</vector>
