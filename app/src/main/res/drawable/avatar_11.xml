<!--
  ~ Copyright 2015 Google Inc. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="128dp"
    android:height="128dp"
    android:viewportWidth="128.0"
    android:viewportHeight="128.0">
    <path
        android:fillColor="#FFCC80"
        android:pathData="M41.6 123.8s0 .1,-.1.1l.3,-.4c-.1.2,-.1.2,-.2.3z" />
    <path
        android:fillColor="#8C9EFF"
        android:pathData="M0 0h128v128h-128z" />
    <path
        android:fillColor="#C2C2C2"
        android:pathData="M34.8 79.5c-2.5,-3.4,-5.9,-6.4,-6.9,-10.3v-.1l-.6,-.5c-.3,-1.2.6,-2 .5,-3l-2.4,-1h-6.8999999999999995s0 17 17.4 17.3c-.9,-.9,-.7,-1.8,-1.1,-2.4z" />
    <path
        android:fillColor="#CFD8DC"
        android:pathData="M21.9 64.2l-.1,-.3c0,-.1 0,-.2,-.1,-.4v-1l.1,-.3c.1,-.1.1,-.2.1,-.3.1,-.1.1,-.2.1,-.2.2,-.4.1,-.8,-.2,-1.1,-.4,-.4,-1,-.4,-1.3 0l-.2.2,-.2.2c-.1.1,-.2.2,-.3.4l-.3.6,-.3.6c-.1.2,-.2.4,-.2.7 0 .2,-.1.5,-.1.8v.5h3c.2,-.2.1,-.3 0,-.4z" />
    <path
        android:fillColor="#eee"
        android:pathData="M116.5 65.2c.1.1.2.1.2.2 0,-.1,-.1,-.2,-.2,-.2zM124.7 71.3l.6.3c-.3,-.1,-.4,-.2,-.6,-.3zM126.4 72.3l.6.3c-.2 0,-.4,-.1,-.6,-.3zM123 70.2l.5.3c-.2 0,-.4,-.2,-.5,-.3zM115 63.7zM114.9 127.8c-12,-17.2,-7.6,-52,-3.1,-67.6v-.1c-3.5,-4.2,-7.8,-11.7,-10.2,-18.7,-1.7,-5,-4,-8.8,-6.4,-11.8l-1.6,-2,-10.7 11.8c-1.5 2.1,-3.3 1.8,-4.1,-.6l-7.1,-17.3c-.3,-.9,-.3,-1.9 0,-2.7.3,-1.1,-.1,-2.1.7,-3l-2,-.1c-1.3 0,-2.5.1,-3.7.3,-1.7,-.3,-3.4,-.5,-5.1,-.5,-11.9 0,-21.9 8.1,-24.8 19.2,-.5 1.8,-.7 3.7,-.8 5.6,-1.2 3.6,-4.2 7.7,-11.5 8.4 1.3.4 2.2 1.9 2 3.6l-.5 2.8c-.3 2,-1.2 2.4,-2.2.9l1.6 8.6.2.9c.1 1.1.3 2.2.6 3.4 1 4 3.2 8.2 7.8 10.9.3.6 1 1.3 2 2.1 8.2 6.7 36 20.7 27.8 46.1h51.3c0,-.1,-.1,-.1,-.2,-.2zm-80.4,-68.5c.5 0 1 .4 1 1 0 .5,-.4 1,-1 1,-.5 0,-1,-.4,-1,-1s.4,-1 1,-1zm7.4 3.9c.5 0 1 .4 1 1 0 .5,-.4 1,-1 1,-.5 0,-1,-.4,-1,-1 0,-.5.5,-1 1,-1zm-1,-8.5c-.5 0,-1,-.4,-1,-1 0,-.5.4,-1 1,-1 .5 0 1 .4 1 1s-.5 1,-1 1zm3.9,-10.9c-.9 0,-1.6,-.7,-1.6,-1.6 0,-.9.7,-1.6 1.6,-1.6.9 0 1.6.7 1.6 1.6 0 .9,-.7 1.6,-1.6 1.6zM118.1 66.5c.1.1.2.2.4.3,-.2,-.1,-.3,-.2,-.4,-.3zM121.3 69.1l.5.3,-.5,-.3zM119.7 67.8l.4.3c-.1 0,-.3,-.1,-.4,-.3z" />
    <path
        android:fillColor="#C2C2C2"
        android:pathData="M114.9 127.8l.2.2h12.9v-54.8l-1,-.6,-.6,-.3,-1.2,-.7,-.6,-.3,-1.2,-.7,-.5,-.3,-1.2,-.8,-.5,-.3,-1.2,-.9,-.4,-.3,-1.2,-1c-.1,-.1,-.2,-.2,-.4,-.3l-1.3,-1.2c-.1,-.1,-.2,-.1,-.2,-.2l-1.5,-1.5c-1.5,-1.6,-3,-3.3,-4.4,-5.1,-4.6 15.4,-13 52.7 4.3 69.1z" />
    <path
        android:fillColor="#646464"
        android:pathData="M26 55.1l.4,-2.8c.2,-1.8,-.6,-3.2,-2,-3.6,-.3,-.1,-.7,-.2,-1.1,-.1,-2 .1,-2.7 1.9,-1.6 3.8l1.8 3.3c.1.2.2.3.3.4 1 1.3 1.9 1 2.2,-1z" />
    <path
        android:fillColor="#444"
        android:pathData="M44.8,42.2 m-2.0, 0 a 2.0,2.0 0 1,1 4.0,0 a2.0,2.0 0 1,1 -4.0,0" />
    <path
        android:fillColor="#CFD8DC"
        android:pathData="M34.5,60.3 m-1.0, 0 a 1.0,1.0 0 1,1 2.0,0 a1.0,1.0 0 1,1 -2.0,0" />
    <path
        android:fillColor="#CFD8DC"
        android:pathData="M40.9,53.7 m-1.0, 0 a 1.0,1.0 0 1,1 2.0,0 a1.0,1.0 0 1,1 -2.0,0" />
    <path
        android:fillColor="#CFD8DC"
        android:pathData="M41.9,64.2 m-1.0, 0 a 1.0,1.0 0 1,1 2.0,0 a1.0,1.0 0 1,1 -2.0,0" />
    <path
        android:fillColor="#646464"
        android:pathData="M70.7 18.8c-.3.8,-.3 1.8 0 2.7l8.1 19.3c.8 2.5 2.6 2.7 4.1.6l12.3,-11.8 1.4,-1.3c.3,-.4.5,-.9.6,-1.3.4,-1.2.3,-2.4,-.1,-3.6,-1.1,-4.3,-5.6,-8.9,-11.2,-10.3,-5.6,-1.4,-10.9,-.2,-13.6 2.7,-.8.8,-1.3 1.8,-1.6 3z" />
</vector>
