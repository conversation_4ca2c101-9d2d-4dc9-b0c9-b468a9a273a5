<!--
  ~ Copyright 2015 Google Inc. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="128dp"
    android:height="128dp"
    android:viewportWidth="128.0"
    android:viewportHeight="128.0">
    <path
        android:fillColor="#FF8A80"
        android:pathData="M0 0h128v128h-128z" />
    <path
        android:fillColor="#F2A600"
        android:pathData="M30.3 43.7c3.1,-.4 6.1 1 7.7 3.4 10.3,-2 11,-11.5 12.6,-14.1 4.5 5.8 13.3 17.8 39.5 14.5 1.6,-2.7 4.7,-4.3 8,-3.8.4 0 .7,-1.9 1,-1.8,-.1,-2.2,-.3,-4.4,-.8,-6.4v-.1c-3.4,-15.8,-17.5,-27.6,-34.3,-27.6,-15 0,-27.8 9.4,-32.8 22.6,-1.4 3.6,-2.2 7.6,-2.3 11.7l.7 1.8.7,-.2z" />
    <path
        android:fillColor="#F2A600"
        android:pathData="M41.5 101.3l.8.7c-.3,-.3,-.5,-.5,-.8,-.7zM42.9 102.4c.2.2.5.4.8.6,-.3,-.2,-.6,-.4,-.8,-.6zM44.2 103.5c.2.2.5.4.8.5,-.2,-.1,-.5,-.3,-.8,-.5zM98.3 35.4zM38.7 98.3c.4.4.8.8 1.1 1.3,-.4,-.4,-.8,-.8,-1.1,-1.3zM31.1 30.4zM46.5 105.1l.5.3,-.5,-.3zM51.1 107.5l.3.1c-.1 0,-.2 0,-.3,-.1zM48 105.9l.5.3c-.2,-.1,-.4,-.2,-.5,-.3zM61.6 110.5l-.9,-.1.9.1zM88.9 67c-1.6 1.4,-3.1 2.7,-4.4 3.9,-3.3 3.8,-6.3 10.3,-1 18.7 5.7,-8.1 10.6,-17.7 13.2,-29.1l-7.8 6.5zM41 100.8l-.8,-.8.8.8zM49.5 106.8l.4.2c-.1,-.1,-.2,-.2,-.4,-.2zM59 110.1h.1,-.1zM12.9 127.4c4.5,-7.1 8.6,-12.7 11.7,-16.6 1.5,-2 2.8,-3.5 3.7,-4.5 1,-1.1 1.5,-1.7 1.5,-1.7l.2,-.2c2.4,-2.7 5.3,-4.5 8.4,-5.4.1,-.2.2,-.4.2,-.5 0,-.1 0,-.2.1,-.2 13.6,-13.3 4.7,-26.5,-1.5,-32.9l-4.4,-3.8c-2.6 8.7,-8.5 18.2,-20.1 26.9,-6.4 4.1,-10.6 11.4,-10.6 19.7 0 8.2 4.1 15.4 10.4 19.8l.4,-.6z" />
    <path
        android:fillColor="#FFE0B2"
        android:pathData="M67.1 110.9h-2 2zM38.8 98.1c-.4.3 2.1 5.6 2.5 5.9 1.8 1.8 4.1 2.2 6.3 3.5 4.8 2.7 10.2 3.6 15.6 4.2 5.7.6 11.5,-.5 16.8,-2.6 2.5,-1 4.8,-2.3 7,-3.8 1.2,-.8 2.2,-1.6 3.3,-2.6.4,-.3 2.4,-3.6 2.7,-3.3,-4,-3.3,-8,-6.9,-10.4,-11.6,-2.2,-4.2,-2.6,-9.1,-.5,-13.4 1.7,-3.3 4.5,-5.6 7.3,-7.9 2.2,-1.8 4.3,-3.6 6.5,-5.5l4.7,-3.9c1,-.8 2.1,-1.6 2.8,-2.7 1.5,-2 1.9,-4.6 1.2,-7,-1.4,-4.8,-7.1,-7.2,-11.5,-4.9,-1 .5,-1.9 1.3,-2.6 2.2l-.5.7,-1.1.1c-.8.1,-1.6.2,-2.4.2,-2.6.2,-5.3.3,-7.9.1,-6.8,-.3,-13.5,-2.1,-19.1,-6,-3.6,-2.4,-6.3,-5.6,-8.9,-9,-.5.9,-.8 1.9,-1.1 2.8,-.5 1.4,-1 2.7,-1.6 4,-1.7 3.3,-4.5 5.8,-8.2 6.9,-.5.2,-1.1.3,-1.7.4,-.3,-.5,-.7,-.9,-1.1,-1.3,-.9,-.6,-2.1,-1.3,-3.3,-1.6,-2.5,-.7,-5.2,-.2,-7.2 1.4,-3.9 3.1,-4 9.2,-.1 12.5l10.5 8.9c2.4 2 4.3 4.8 5.9 7.4 2.1 3.5 3.4 7.6 3.2 11.7,-.3 5.6,-3.3 10.4,-7.1 14.2,-.1.1 13.6,-13.3 0 0z" />
    <path
        android:fillColor="#80DEEA"
        android:pathData="M93 99.4l-.2,-.1,-1.5 1.5,-.5.4c-.4.4,-.9.8,-1.3 1.1l-.4.3c-.6.5,-1.2.9,-1.8 1.3l-.3.2c-.5.4,-1.1.7,-1.6 1l-.5.3,-1.8 1,-.2.1c-.7.4,-1.4.7,-2.1 1l-.4.2,-1.8.7,-.5.2c-.8.3,-1.5.5,-2.3.7l-2.4.6c-.1 0,-.2 0,-.3.1l-2.1.4h-.2c-1.5.2,-3 .3,-4.5.4h2,-1.9c-.6 0,-1.2 0,-1.9,-.1l-2.7,-.3,-.9,-.1,-1.6,-.3h-.1c-2.7,-.6,-5.2,-1.4,-7.6,-2.4l-.3,-.1c-.4,-.2,-.8,-.4,-1.2,-.5l-.4,-.2c-.4,-.2,-.7,-.4,-1,-.5l-.5,-.3,-1,-.6,-.5,-.3c-.5,-.3,-1,-.6,-1.5,-1,-.3,-.2,-.5,-.4,-.8,-.5l-.6,-.5,-.8,-.6,-.6,-.5c-.3,-.2,-.5,-.4,-.8,-.7l-.5,-.5,-.8,-.8,-.4,-.4c-.4,-.4,-.8,-.8,-1.1,-1.3,-.1.2,-.2.4,-.2.5,-3.1.9,-6 2.7,-8.4 5.4l-.2.2s-.5.6,-1.5 1.7c-.9 1.1,-2.2 2.6,-3.7 4.5,-3.1 3.9,-7.2 9.5,-11.7 16.6,-.1.2,-.2.4,-.4.6h105.3c-2.7,-5.4,-5.1,-9.8,-7.1,-13.1,-1.3,-2.2,-2.3,-3.9,-3.1,-5.1,-.9,-1.3,-1.3,-2,-1.3,-2l-.2,-.3c-.6,-.9,-1.2,-1.7,-1.9,-2.4,-3.3,-3.3,-7.4,-5.2,-11.4,-5.5z" />
    <path
        android:fillColor="#444"
        android:pathData="M78.9,51.3 m-2.0, 0 a 2.0,2.0 0 1,1 4.0,0 a2.0,2.0 0 1,1 -4.0,0" />
    <path
        android:fillColor="#444"
        android:pathData="M53.2,51.3 m-2.0, 0 a 2.0,2.0 0 1,1 4.0,0 a2.0,2.0 0 1,1 -4.0,0" />
</vector>
