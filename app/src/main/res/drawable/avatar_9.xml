<!--
  ~ Copyright 2015 Google Inc. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="128dp"
    android:height="128dp"
    android:viewportWidth="128.0"
    android:viewportHeight="128.0">
    <path
        android:fillColor="#FFCC80"
        android:pathData="M41.6 123.8s.1,-.2.2,-.3c-.1.2,-.1.2,-.2.3z" />
    <path
        android:fillColor="#B388FF"
        android:pathData="M0 0h128v128h-128z" />
    <path
        android:fillColor="#2A56C6"
        android:pathData="M98.8 94.8v.2,-.2z" />
    <path
        android:fillColor="#FFE0B2"
        android:pathData="M2.8 109.6l-2.8 1.2v17.2h15.5l-12.7,-18.4z" />
    <path
        android:fillColor="#DD2C00"
        android:pathData="M91.9 128h5.6c-3.8,-12,-4.4,-16,-4.9,-20.1,-.1,-.5,-.5,-2.4,-.5,-2.9,-2.6.7,-5.2 1.1,-8 1.2,-8.7,-.2,-16.5,-3.8,-22.3,-9.3l-.2,-.2,-.1,-.1c-2.9.2,-7.6.2,-10.8.6,-4.6.6,-9.6 1.3,-15 2.4,-4.7.9,-11.2 2.7,-33.3 9.3l.5.7 12.6 18.4h70.2" />
    <path
        android:fillColor="#FFEB3B"
        android:pathData="M66.2 86.5c0,-.1 0,-.1.1,-.2.1,-.6.3,-1.1.4,-1.6v-.2l.3,-1.6v-.1l.2,-1.6c1,-10.6,-2.4,-17.3,-5.2,-20.4,-.1,-.1,-.2,-.3,-.4,-.4l-.1,-.1,-.3,-.3,-.1,-.1,-.3,-.3,-.1,-.1,-.2,-.2,-.1,-.1c-.1,-.1,-.1,-.1,-.2,-.1 0 0,-.1 0,-.1,-.1l-.1,-.1s-.1 0,-.1,-.1c-1.6,-1.7,-3.3,-3,-4.9,-4.1,-.9,-.6,-1.7,-1.2,-2.5,-1.6,-1.8,-1.1,-3.2,-1.6,-3.4,-1.7,-1.3,-1.2,-2.3,-3,-2.6,-5,-.6,-4.4.3,-6.4 4.3,-7.1 1,-.2 2,-.1 3 .2l.1,-.7c.6,-.1 1.1,-.2 1.6,-.4l.5,-.1 1.1,-.3.5,-.2 1,-.3.4,-.2c.4,-.1.7,-.3 1.1,-.5l.2,-.1 1.2,-.6.3,-.2c.3,-.1.5,-.3.8,-.5.1,-.1.3,-.2.4,-.2.2,-.1.4,-.3.7,-.4.1,-.1.2,-.2.3,-.2l.7,-.5c.1,-.1.2,-.1.2,-.2.3,-.2.5,-.4.8,-.7.1,-.1.2,-.1.2,-.2l.5,-.5.3,-.3.4,-.4.3,-.3.3,-.4c.1,-.1.2,-.2.2,-.3.2,-.2.3,-.4.4,-.6l.6,-.9.1,-.2c.2,-.3.3,-.5.5,-.7 4.2 5.9 14.5 10.4 39.3 6.7,-4.2,-14,-17.3,-23.5,-31.3,-21.4,-5.3.8,-10.1 3.2,-14 6.6l-.2.2c-.5.4,-1 .9,-1.4 1.4,-5.3 4.8,-23.3 19.8,-52.2 26.5,-3.9 0,-7.4 1.6,-9.9 4.2v19.3c.3.3.6.6.9.8l-.3.9s30.5 22.8 63.6 16.7l1.6,-1.8c0,-.1 0,-.1.1,-.2.2,-.7.4,-1.3.5,-1.8z" />
    <path
        android:fillColor="#FFE0B2"
        android:pathData="M84 106.2c2.8,-.1 5.4,-.5 8,-1.2l.8,-3.8c4.3,-19.3 9.7,-37.4 15,-52.9h6.9l-4.8,-8.2c-.2,-1.9,-.6,-3.6,-1.2,-5.3,-24.8 3.7,-35,-2.5,-39.1,-8.4,-.3.5,-.7 1.1,-1.2 1.8l-.4.6,-.2.3,-.3.4,-.3.3,-.4.4,-.3.3,-.5.5s-.1 0,-.2.1c-.3.2,-.5.4,-.8.7,-.1.1,-.2.1,-.2.2,-.2.2,-.4.3,-.7.5,-.1.1,-.2.2,-.3.2,-.2.1,-.4.3,-.7.4,-.1.1,-.2.2,-.4.2l-.8.5,-.3.2,-1.2.6,-.2.1,-1.1.5,-.4.2c-.3.1,-.6.2,-1 .3l-.5.2c-.3.1,-.7.2,-1.1.3l-.5.1,-1.6.4c-.1.2,-.1.5,-.1.7,-.9,-.3,-1.9,-.4,-3,-.2,-4.1.6,-6.9 4.7,-6.3 9.1.3 2 1.2 3.8 2.6 5 .3.1 1.6.7 3.4 1.7.8.4 1.6 1 2.5 1.6 1.5 1.1 3.2 2.5 4.9 4.1l.1.1.1.1.1.1s.1.1.2.1c0 0 .1 0 .1.1l.2.2.1.1.3.3.1.1.3.3.1.1s.2.3.4.4c2.7 3.1 7.2 9.8 6.2 20.4l-.2 1.6v.1l-.3 1.6v.2c-.1.5,-.2 1.1,-.4 1.6 0 .1 0 .1,-.1.2,-.1.6,-.3 1.1,-.5 1.7 0 .1 0 .1,-.1.2,-.8 2.6,-1.8 5.3,-3.3 8.3.1.1.2.2.4.3 5.7 5.6 13.5 9.1 22.2 9.3z" />
    <path
        android:fillColor="#DD2C00"
        android:pathData="M85.6 128h5.1" />
    <path
        android:fillColor="#444"
        android:pathData="M95.8,46.5 m-2.0, 0 a 2.0,2.0 0 1,1 4.0,0 a2.0,2.0 0 1,1 -4.0,0" />
</vector>
