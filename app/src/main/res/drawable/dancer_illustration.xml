<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="200dp"
    android:height="250dp"
    android:viewportWidth="200"
    android:viewportHeight="250">
    
    <!-- Background circle -->
    <path
        android:fillColor="#FFE8D5C4"
        android:pathData="M100,50 C130,50 155,75 155,105 C155,135 130,160 100,160 C70,160 45,135 45,105 C45,75 70,50 100,50 Z" />
    
    <!-- Body -->
    <path
        android:fillColor="#FFCD9777"
        android:pathData="M85,90 Q100,85 115,90 L120,140 Q100,145 80,140 Z" />
    
    <!-- Head -->
    <path
        android:fillColor="#FFCD9777"
        android:pathData="M100,60 C108.3,60 115,66.7 115,75 C115,83.3 108.3,90 100,90 C91.7,90 85,83.3 85,75 C85,66.7 91.7,60 100,60 Z" />
    
    <!-- Hair -->
    <path
        android:fillColor="#FF8B4513"
        android:pathData="M85,65 Q100,55 115,65 Q120,70 115,75 Q100,70 85,75 Q80,70 85,65 Z" />
    
    <!-- Arms -->
    <path
        android:fillColor="#FFCD9777"
        android:pathData="M80,100 Q70,95 65,105 Q70,115 80,110" />
    <path
        android:fillColor="#FFCD9777"
        android:pathData="M120,100 Q130,95 135,105 Q130,115 120,110" />
    
    <!-- Legs -->
    <path
        android:fillColor="#FF2C5F2D"
        android:pathData="M85,140 Q80,160 85,180 Q90,185 95,180 Q90,160 95,140" />
    <path
        android:fillColor="#FF2C5F2D"
        android:pathData="M105,140 Q110,160 105,180 Q100,185 95,180 Q100,160 105,140" />
    
    <!-- Dance pose elements -->
    <path
        android:fillColor="#FFCD9777"
        android:pathData="M95,180 Q100,190 105,180 Q110,185 105,190 Q100,195 95,190 Q90,185 95,180 Z" />
    
</vector>
