<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mobile_navigation"
    app:startDestination="@+id/nav_transform">

    <fragment
        android:id="@+id/nav_transform"
        android:name="com.yakri.dancingdivas.ui.transform.TransformFragment"
        android:label="@string/menu_transform"
        tools:layout="@layout/fragment_transform" />

    <fragment
        android:id="@+id/nav_reflow"
        android:name="com.yakri.dancingdivas.ui.reflow.ReflowFragment"
        android:label="@string/menu_reflow"
        tools:layout="@layout/fragment_reflow" />

    <fragment
        android:id="@+id/nav_slideshow"
        android:name="com.yakri.dancingdivas.ui.slideshow.SlideshowFragment"
        android:label="@string/menu_slideshow"
        tools:layout="@layout/fragment_slideshow" />

    <fragment
        android:id="@+id/nav_settings"
        android:name="com.yakri.dancingdivas.ui.settings.SettingsFragment"
        android:label="@string/menu_settings"
        tools:layout="@layout/fragment_settings" />
</navigation>